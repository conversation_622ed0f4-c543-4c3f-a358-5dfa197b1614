{
    // 使用 IntelliSense 了解相关属性。
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387

    "version": "0.2.0",
    "configurations": [
        {
            "name": "Cortex Debug",
            "cwd": "${workspaceFolder}",
            "executable": "./build/mc02_test.elf",
            "request": "launch",
            "type": "cortex-debug",
            "runToEntryPoint": "main",
            "servertype": "pyocd",
            "targetId": "stm32h723vgtx",
            "serverArgs": [
                "--target", "stm32h723vgtx",
                "--frequency", "4000000",
                "--pack", "${workspaceFolder}/Keil.STM32H7xx_DFP.4.1.0.pack"
            ],
            "serverTimeout": 30000,
            "gdbTimeout": 20000,
            "armToolchainPath": "/usr/bin",
            "svdFile": "${workspaceFolder}/Keil.STM32H7xx_DFP.4.1.0.pack/SVD/STM32H723.svd",
            "showDevDebugOutput": "both",
            "preLaunchCommands": [
                "set mem inaccessible-by-default off",
                "set architecture armv7e-m"
            ],
            "postAttachCommands": [
                "monitor reset halt"
            ]
        }
    ]
}